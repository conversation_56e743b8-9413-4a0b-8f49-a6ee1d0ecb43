<template>
  <LineChart
    ref="chartRef"
    class="mt-4"
    title="CPS1、CPS2全天及江苏频率曲线"
    :data="chartData"
    height="400px"
    :areaStyle="false"
    :custom-config="customConfig"
  />
</template>

<script setup lang="ts">
import LineChart from '@/components/shared/charts/LineChart.vue'
import { useCpsFrequency } from '@/stores'
import type { SeriesData } from '@/types'
import { computed, onMounted, provide, useTemplateRef } from 'vue'

const cpsFrequencyStore = useCpsFrequency()
const chartRef = useTemplateRef('chartRef')

const customConfig = {
  series: {
    symbol: 'emptyCircle',
  },
  legend: {
    icon: undefined,
    itemHeight: 10,
  },
}

// 图表数据
const chartData = computed<SeriesData[]>(() => {
  if (!cpsFrequencyStore.cpsData) return []

  return [
    {
      name: '全天CPS1',
      color: '#3CB670',
      data: cpsFrequencyStore.cpsData.CPS1List.map((item) => ({
        name: item.time,
        value: +item.value || 0,
      })),
    },
    {
      name: '全天CPS2',
      color: '#2585E6',
      data: cpsFrequencyStore.cpsData.CPS2List.map((item) => ({
        name: item.time,
        value: +item.value || 0,
      })),
    },
    {
      name: '频率',
      color: '#D240A6',
      yAxisIndex: 1,
      data: cpsFrequencyStore.cpsData.JSRateList.map((item) => ({
        name: item.time,
        value: +item.value || 0,
      })),
    },
  ]
})

onMounted(() => {
  cpsFrequencyStore.chartRef = chartRef.value
})
</script>

<style scoped></style>
