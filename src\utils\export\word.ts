/**
 * Word 文档导出工具
 * 支持文本、表格、图片等多种内容类型
 */
import type { WordContentItem } from '@/types'
import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  Table,
  TableCell,
  TableRow,
  ImageRun,
  AlignmentType,
  WidthType,
  BorderStyle,
  convertInchesToTwip,
} from 'docx'
import { saveAs } from './index'

/**
 * 生成文本段落
 */
export const generateText = (item: WordContentItem & { type: 'text' }) => {
  const { data, style = {}, textStyle = {} } = item

  // 处理首行缩进
  const indentFirstLine = style.indentFirstLine ? convertInchesToTwip(0.5) : undefined

  // 处理编号配置
  const numberingConfig = style.numbering
    ? {
        reference: style.numbering.reference,
        level: style.numbering.level,
      }
    : undefined

  return new Paragraph({
    heading: style.heading,
    alignment: style.alignment || AlignmentType.LEFT,
    spacing: style.spacing,
    numbering: numberingConfig,
    indent: {
      firstLine: indentFirstLine,
    },
    children: [
      new TextRun({
        text: data,
        font: textStyle.font || '仿宋_GB2312',
        size: textStyle.size || 32,
        bold: textStyle.bold || false,
        color: textStyle.color || '#000000',
      }),
    ],
  })
}

/**
 * 生成表格
 */
export const generateTable = (item: WordContentItem & { type: 'table' }) => {
  const { data, style = {} } = item

  if (!data || data.length === 0) {
    return new Paragraph('')
  }

  const totalWidthTwip = convertInchesToTwip(6) // 6 inch 表格宽度，适合 A4 页面
  const columnCount = data[0].length
  const cellWidth = Math.floor(totalWidthTwip / columnCount)

  const rows = data.map((rowData, rowIndex) => {
    return new TableRow({
      children: rowData.map((cellData) => {
        return new TableCell({
          width: {
            size: cellWidth,
            type: WidthType.DXA,
          },
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: cellData,
                  size: style.fontSize || 24,
                  bold: rowIndex === 0,
                }),
              ],
              alignment: AlignmentType.CENTER,
            }),
          ],
          margins: {
            top: convertInchesToTwip(0.05),
            bottom: convertInchesToTwip(0.05),
            left: convertInchesToTwip(0.1),
            right: convertInchesToTwip(0.1),
          },
        })
      }),
    })
  })

  return new Table({
    width: {
      size: totalWidthTwip,
      type: WidthType.DXA,
    },
    columnWidths: Array(columnCount).fill(cellWidth),
    rows,
    borders: {
      top: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
      bottom: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
      left: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
      right: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
      insideHorizontal: {
        style: BorderStyle.SINGLE,
        size: 1,
        color: style.borderColor || '#000000',
      },
      insideVertical: { style: BorderStyle.SINGLE, size: 1, color: style.borderColor || '#000000' },
    },
  })
}

/**
 * 将 base64 字符串转为 Uint8Array（用于浏览器）
 */
function base64ToUint8Array(base64: string): Uint8Array {
  const binaryString = atob(base64)
  const len = binaryString.length
  const bytes = new Uint8Array(len)
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  return bytes
}

/**
 * 生成图片
 */
export const generateImage = (item: WordContentItem & { type: 'image' }) => {
  if (!item.data) {
    return new Paragraph('')
  }
  let { width = 600, height = 300 } = item.style || {}

  // 如果图片宽度大于页面宽度，自动缩放
  const maxWidth = 600
  if (width > maxWidth) {
    height = (height / width) * maxWidth
    width = maxWidth
  }

  // 移除 base64 前缀并确定图片类型
  const base64Data = item.data.replace(/^data:image\/([a-z]+);base64,/, '')
  const imageType = item.data.match(/^data:image\/([a-z]+);base64,/)?.[1] || 'png'
  const imageBuffer = base64ToUint8Array(base64Data)

  return new Paragraph({
    children: [
      new ImageRun({
        data: imageBuffer,
        transformation: {
          width,
          height,
        },
        type: imageType as any,
      }),
    ],
    alignment: AlignmentType.CENTER,
    spacing: {
      before: 200,
      after: 200,
    },
  })
}

/**
 * 导出 Word 文档
 */
export const exportToWord = async (filename = '导出文档.docx', data: WordContentItem[]) => {
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  const children = data.map((item) => {
    switch (item.type) {
      case 'text':
        return generateText(item as WordContentItem & { type: 'text' })
      case 'table':
        return generateTable(item as WordContentItem & { type: 'table' })
      case 'image':
        return generateImage(item as WordContentItem & { type: 'image' })
      default:
        return new Paragraph('')
    }
  })

  const doc = new Document({
    sections: [
      {
        children,
      },
    ],
  })

  const blob = await Packer.toBlob(doc)
  saveAs(blob, filename)
}
