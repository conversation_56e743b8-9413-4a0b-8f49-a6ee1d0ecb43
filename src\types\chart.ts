// 图表相关类型定义

// 数据点接口
export interface DataPoint {
  name: string
  value: number
}

// 渐变色配置接口
export interface GradientColors {
  start: string
  end: string
}

// 系列数据接口
export interface SeriesData {
  name: string
  data: DataPoint[]
  color?: string
  yAxisIndex?: number
  gradientColors?: GradientColors
}

// 自定义配置接口
export interface CustomChartConfig {
  // 系列配置
  series?: {
    symbol?:
      | string
      | 'none'
      | 'circle'
      | 'rect'
      | 'roundRect'
      | 'triangle'
      | 'diamond'
      | 'pin'
      | 'arrow'
    symbolSize?: number
    lineStyle?: {
      width?: number
      type?: 'solid' | 'dashed' | 'dotted'
    }
    itemStyle?: any
    areaStyle?: any
  }
  // 图例配置
  legend?: {
    icon?:
      | string
      | 'circle'
      | 'rect'
      | 'roundRect'
      | 'triangle'
      | 'diamond'
      | 'pin'
      | 'arrow'
      | 'none'
    itemWidth?: number
    itemHeight?: number
    orient?: 'horizontal' | 'vertical'
    left?: string | number
    right?: string | number
    top?: string | number
    bottom?: string | number
    textStyle?: any
  }
  // 标题配置
  title?: {
    textStyle?: {
      fontSize?: number
      fontWeight?: string | number
      color?: string
    }
    left?: string | number
    top?: string | number
  }
  // 提示框配置
  tooltip?: any
  // 网格配置
  grid?: any
  // X轴配置
  xAxis?: any
  // Y轴配置
  yAxis?: any
}

// LineChart 组件 Props 接口
export interface LineChartProps {
  title?: string
  data: SeriesData[]
  width?: string
  height?: string
  xAxisData?: string[]
  showGrid?: boolean
  showLegend?: boolean
  smooth?: boolean
  areaStyle?: boolean
  customConfig?: CustomChartConfig
}

// 图表配置选项接口
export interface ChartOption {
  title?: any
  tooltip?: any
  legend?: any
  grid?: any
  xAxis?: any
  yAxis?: any
  series?: any[]
}

// 默认渐变色配置
export const DEFAULT_GRADIENT_COLORS: GradientColors[] = [
  {
    start: 'rgba(19, 134, 223, 1)',
    end: 'rgba(255, 255, 255, 0.20)',
  },
  {
    start: 'rgba(78, 0, 155, 1)',
    end: 'rgba(255, 255, 255, 0.20)',
  },
  {
    start: 'rgba(46,204,113,0.8)',
    end: 'rgba(46,204,113,0.3)',
  },
  {
    start: 'rgba(241,196,15,0.8)',
    end: 'rgba(241,196,15,0.3)',
  },
  {
    start: 'rgba(155,89,182,0.8)',
    end: 'rgba(155,89,182,0.3)',
  },
  {
    start: 'rgba(52,152,219,0.8)',
    end: 'rgba(52,152,219,0.3)',
  },
]

// 图表主题配置
export interface ChartTheme {
  backgroundColor?: string
  textColor?: string
  gridColor?: string
  axisLineColor?: string
  splitLineColor?: string
}

// 默认主题
export const DEFAULT_CHART_THEME: ChartTheme = {
  backgroundColor: '#ffffff',
  textColor: '#5F6673',
  gridColor: '#E4E7ED',
  axisLineColor: '#E4E7ED',
  splitLineColor: '#F2F6FC',
}

export interface ChartImageConfig {
  width: number
  height: number
  base64: string
}
