/**
 * 导出工具类
 */
import ExcelJS from 'exceljs'
import type { CPSStatisticItem } from '../api'
import { saveAs } from '.'

export interface ExportColumn {
  key: string
  title: string
  width?: number
  formatter?: (value: any) => string
}

export interface ExportOptions {
  filename?: string
  sheetName?: string
  columns?: ExportColumn[]
  data: any[]
}

/**
 * 导出为Excel格式,使用 xlsx 库
 * 表头使用columns
 */
export const exportToExcel = async (options: ExportOptions) => {
  console.log('🚀 ~ exportToExcel ~ options:', options)
  const { data, columns = [], filename = 'export_data', sheetName = 'Sheet1' } = options

  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  const workbook = new ExcelJS.Workbook()
  const sheet = workbook.addWorksheet(sheetName)

  // 设置列
  sheet.columns = columns.map((col) => ({
    header: col.title,
    key: col.key,
    width: col.width || col.title.length + 2,
  }))

  // 插入数据
  data.forEach((row) => {
    const newRow: Record<string, any> = {}
    columns.forEach((col) => {
      const rawValue = row[col.key]
      newRow[col.key] = col.formatter ? col.formatter(rawValue) : rawValue
    })
    sheet.addRow(newRow)
  })

  // 样式：加粗表头
  sheet.getRow(1).font = { bold: true }

  // 自动调整列宽（如果没手动指定）
  sheet.columns.forEach((col) => {
    let maxLen = col.header?.toString().length || 10
    sheet.eachRow((row) => {
      const cell = row.getCell(col.key as string)
      const len = cell.value?.toString().length || 0
      if (len > maxLen) maxLen = len
    })
    col.width = maxLen + 2
  })

  // 导出文件
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  })
  saveAs(blob, `${filename}.xlsx`)
}

/**
 * cps 数据导出
 * @param CPSStatisticList
 * @param JSRateList
 * @param filename
 * @param base64Image
 */
export const exportCpsExcel = async (
  CPSStatisticList: CPSStatisticItem[],
  JSRateList: any[],
  filename: string,
  base64Image: string,
) => {
  if (!CPSStatisticList || CPSStatisticList.length === 0) {
    throw new Error('没有数据可导出')
  }

  const workbook = new ExcelJS.Workbook()
  const sheet = workbook.addWorksheet('数据')

  // 插入图表图片（如果有）
  if (base64Image) {
    const base64 = base64Image.replace(/^data:image\/png;base64,/, '')

    const imageId = workbook.addImage({
      base64,
      extension: 'png',
    })

    sheet.addImage(imageId, {
      tl: { col: 0, row: 0 },
      ext: { width: 600, height: 300 },
    })
  }

  // 图片估算占 20 行
  let currentRow = 18

  // 2️⃣ 每个日期一个区块
  for (const item of CPSStatisticList) {
    // 日期行
    const dateTitleRow = sheet.getRow(currentRow)
    dateTitleRow.getCell(1).value = '日期：'
    dateTitleRow.getCell(2).value = item.date
    dateTitleRow.font = { bold: true }

    // 设置背景色
    dateTitleRow.getCell(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFACB9CA' }, // 注意要加 FF 表示不透明
    }
    dateTitleRow.getCell(2).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFACB9CA' }, // 注意要加 FF 表示不透明
    }

    currentRow++

    const row = sheet.getRow(currentRow++) // 只写一行
    const titles = ['CPS1', 'CPS2', '全日电费', '最大频率', '最小频率']
    const values = [item.cps1, item.cps2, item.allDayFee, item.maxRate, item.minRate]

    for (let i = 0; i < titles.length; i++) {
      const colIndex = i * 2 + 1
      row.getCell(colIndex).value = titles[i] + '：'
      row.getCell(colIndex + 1).value = values[i]
    }

    // 时间对齐，构建完整列表
    const timeMap: Record<string, { cps1?: string; cps2?: string; jsrate?: string }> = {}

    item.cps1List?.forEach(({ time, value }) => {
      const old = timeMap[time] || {}
      timeMap[time] = { ...old, cps1: value }
    })

    item.cps2List?.forEach(({ time, value }) => {
      const old = timeMap[time] || {}
      timeMap[time] = { ...old, cps2: value }
    })

    const datePrefix = item.date + ' '
    JSRateList?.forEach(({ time, value }) => {
      if (time.startsWith(datePrefix)) {
        const old = timeMap[time] || {}
        timeMap[time] = { ...old, jsrate: value }
      }
    })

    // 写入数据：改为横向时间
    const sortedTimes = Object.keys(timeMap).sort()

    // 时间表头（横向）
    const headerRow = sheet.getRow(currentRow++)
    headerRow.getCell(1).value = '时间'
    sortedTimes.forEach((time, idx) => {
      headerRow.getCell(idx + 2).value = time
    })

    // cps1 行
    const cps1Row = sheet.getRow(currentRow++)
    cps1Row.getCell(1).value = 'cps1'
    sortedTimes.forEach((time, idx) => {
      cps1Row.getCell(idx + 2).value = timeMap[time]?.cps1 ?? ''
    })

    // cps2 行
    const cps2Row = sheet.getRow(currentRow++)
    cps2Row.getCell(1).value = 'cps2'
    sortedTimes.forEach((time, idx) => {
      cps2Row.getCell(idx + 2).value = timeMap[time]?.cps2 ?? ''
    })

    // 频率行
    const jsrateRow = sheet.getRow(currentRow++)
    jsrateRow.getCell(1).value = '频率'
    sortedTimes.forEach((time, idx) => {
      jsrateRow.getCell(idx + 2).value = timeMap[time]?.jsrate ?? ''
    })

    // 区块之间空一行
    currentRow++
  }

  // 自动设置所有列宽度
  sheet.columns.forEach((column) => {
    let maxLength = 10 // 默认最小宽度

    column.eachCell?.((cell) => {
      const cellValue = String(cell.value ?? '')
      const length = cellValue.length
      if (length > maxLength) {
        maxLength = length
      }
    })

    column.width = maxLength + 2 // 预留2个字符空隙
  })

  // 下载
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  })
  saveAs(blob, `${filename}.xlsx`)
}
