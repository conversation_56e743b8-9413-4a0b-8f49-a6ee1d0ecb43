<template>
  <KeepAlive :include="['ProvinceStatistic']">
    <component :is="isInfoVisible ? ProvinceInfo : ProvinceStatistic"></component>
  </KeepAlive>
</template>

<script setup lang="ts">
import ProvinceStatistic from './statistic/ProvinceStatistic.vue'
import ProvinceInfo from './info/ProvinceInfo.vue'
import { ref } from 'vue'

const isInfoVisible = ref(false)
</script>

<style scoped></style>
