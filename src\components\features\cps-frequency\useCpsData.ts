//  cpsData相关 hook

import { useCpsFrequency } from '@/stores'
import { CPSFrequencyService, type CPSStatisticQueryParams } from '@/utils/api'
import { formatNumber, formatTime, getTodayTimeRange } from '@/utils/tools'
import { onBeforeUnmount, onMounted, ref } from 'vue'

const RT_KEY_LIST = [
  {
    label: 'CPS1',
    value: '130010:320000000000010129',
  },
  {
    label: 'CPS2',
    value: '130010:320000000000010078',
  },
  {
    label: '全天费用',
    value: '130037:320000000000010009',
  },
  {
    label: '频率',
    value: '103054:320000000000900006',
  },
]

export const useCpsData = () => {
  const TIMER_INTERVAL = 1000 * 60
  const cpsFrequencyStore = useCpsFrequency()
  const loading = ref(false)
  const rtKeyData = ref<(string | number)[]>([])

  // 获取CPS数据
  const fetchCPSData = async () => {
    loading.value = true
    const timestampRange = getTodayTimeRange()

    try {
      const params: CPSStatisticQueryParams = {
        startTime: formatTime(timestampRange[0]),
        endTime: formatTime(timestampRange[1]),
      }

      if (cpsFrequencyStore.isInfoVisible) {
        params.startTime = formatTime(cpsFrequencyStore.timeRange[0])
        params.endTime = formatTime(cpsFrequencyStore.timeRange[1])
      }

      const response = await CPSFrequencyService.getCPSStatisticByTime(params)
      cpsFrequencyStore.cpsData = response
    } catch (error) {
      console.error('获取CPS数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchCPSDataByRtKeyId = async () => {
    loading.value = true
    try {
      const response = await CPSFrequencyService.getDataByRtKeyId({
        rtKeyStr: RT_KEY_LIST.map((item) => item.value).join(','),
      })
      rtKeyData.value = response.map((item) => formatNumber(item))
    } catch (error) {
      console.error('获取CPS实时值失败:', error)
    } finally {
      loading.value = false
    }
  }
  let cpsDataTimer: NodeJS.Timeout | null = null

  // 增加fetchCPSData 按指定时间定时刷新
  const fetchCPSDataInterval = () => {
    fetchCPSData()
    cpsDataTimer = setTimeout(fetchCPSDataInterval, TIMER_INTERVAL)
  }

  const clearCPSDataInterval = () => {
    if (cpsDataTimer) {
      clearTimeout(cpsDataTimer)
      cpsDataTimer = null
    }
  }

  let cpsDataByRtKeyTimer: NodeJS.Timeout | null = null
  // 增加fetchCPSDataByRtKeyId 按指定时间定时刷新
  const fetchCPSDataByRtKeyIdInterval = () => {
    fetchCPSDataByRtKeyId()
    cpsDataByRtKeyTimer = setTimeout(fetchCPSDataByRtKeyIdInterval, TIMER_INTERVAL)
  }

  const clearCPSDataByRtKeyIdInterval = () => {
    if (cpsDataByRtKeyTimer) {
      clearTimeout(cpsDataByRtKeyTimer)
      cpsDataByRtKeyTimer = null
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    fetchCPSDataInterval()
    if (cpsFrequencyStore.isInfoVisible) return
    fetchCPSDataByRtKeyIdInterval()
  })

  onBeforeUnmount(() => {
    clearCPSDataInterval()
    clearCPSDataByRtKeyIdInterval()
  })
  return {
    loading,
    rtKeyData,
  }
}
