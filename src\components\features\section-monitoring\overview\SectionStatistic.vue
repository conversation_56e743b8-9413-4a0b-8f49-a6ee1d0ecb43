<template>
  <SearchForm :loading="loading" showReset @search="handleSearch" @reset="handleReset">
    <template #form-fields>
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
        <n-date-picker
          v-model:value="formData.timeRange"
          class="w-full"
          type="datetimerange"
          size="large"
          placeholder="请选择时间"
          :is-date-disabled="disablePreviousDate"
        >
        </n-date-picker>
      </InputGroup>

      <InputGroup size="large" label="名称" class="mr-2.5 max-w-317px">
        <n-input v-model:value="formData.name" size="large" placeholder="请输入名称" clearable>
        </n-input>
      </InputGroup>

      <InputGroup size="large" label="电压" class="mr-2.5 max-w-212px">
        <n-select
          v-model:value="formData.volt"
          class="w-full"
          placeholder=""
          :options="voltOptions"
          size="large"
          clearable
          @update-value="handleVoltChange"
        />
      </InputGroup>

      <InputGroup size="large" label="状态" class="mr-2.5 max-w-212px">
        <n-select
          v-model:value="formData.status"
          size="large"
          placeholder=""
          :options="statusOptions"
          clearable
        />
      </InputGroup>
    </template>

    <template #action-buttons>
      <ExportButton :data="tableData" :columns="columnsByExport" filename="断面统计列表" />
    </template>
  </SearchForm>

  <DataTable
    class="p-5"
    :columns="columns"
    :data="tableData"
    :loading="loading"
    height="calc(100vh - 170px)"
    @sort="handleSort"
  >
    <template #action="{ item, index }">
      <n-button type="primary" @click="handleViewDetail(item as TableRowData, index)">
        查看
      </n-button>
    </template>
  </DataTable>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { NDatePicker, NInput, NButton, NSelect, type SelectOption } from 'naive-ui'
import DataTable from '@/components/shared/DataTable.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import ExportButton from '@/components/shared/ExportButton.vue'
import {
  SectionMonitoringService,
  type SectionStatisticData,
  type SectionStatisticQueryParams,
} from '@/utils/api'

import { useSectionMonitoringStore } from '@/stores/'
import { formatTime, getTodayTimeRange, disablePreviousDate } from '@/utils/tools/'
import {
  applyDefaultSort,
  handleMultiSort,
  compareVoltage,
  compareNumericDesc,
  compareString,
} from '@/utils/sort/'

// 定义数据类型
interface TableRowData {
  id: string
  name: string
  volt: string
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  maxValueTime: string
}

// 定义表格列配置
const columns = [
  {
    key: 'name',
    title: '断面名称',
    align: 'left' as const,
  },
  {
    key: 'volt',
    title: '电压等级',
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'limit',
    title: '限额',
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'maxValue',
    title: '最大潮流',
    align: 'center' as const,
    width: '10%',
    sorter: {
      compare: compareNumericDesc('maxValue'),
      multiple: 2,
    },
  },
  {
    key: 'maxDiffValue',
    title: '差额最大值',
    align: 'center' as const,
    width: '10%',
    sorter: {
      compare: compareNumericDesc('maxDiffValue'),
      multiple: 3,
    },
  },
  {
    key: 'totalOverTime',
    title: '总越限时间',
    align: 'center' as const,
    width: '10%',
    sorter: {
      compare: compareNumericDesc('totalOverTime'),
      multiple: 4,
    },
  },
  {
    key: 'longestOverTime',
    title: '最长出现越限时长',
    align: 'center' as const,
    width: '12%',
    sorter: {
      compare: compareNumericDesc('longestOverTime'),
      multiple: 5,
    },
  },
  {
    key: 'maxValueTime',
    title: '最大潮流越限出现时间',
    align: 'center' as const,
    width: '13%',
  },
  {
    key: 'action',
    title: '详情',
    width: '5%',
    align: 'center' as const,
  },
]

// 定义表格数据
const tableData = ref<TableRowData[]>([])

// 定义加载状态
const loading = ref(false)

// 定义响应式变量
const sectionMonitoringStore = useSectionMonitoringStore()

const timestampRange = getTodayTimeRange()

// 表单数据
const formData = ref<{
  timeRange: [number, number]
  name: string
  volt: string | null
  status: string | null
}>({
  timeRange: [timestampRange[0], timestampRange[1]],
  name: '',
  volt: null,
  status: null,
})

// 选项配置
const voltOptions: SelectOption[] = [
  {
    label: '500KV',
    value: '1003',
  },
  {
    label: '220KV',
    value: '1005',
  },
]

const statusOptions: SelectOption[] = [
  {
    label: '越限',
    value: '1',
  },
  {
    label: '重载',
    value: '2',
  },
]

// 获取断面统计数据
const fetchSectionStatisticList = async () => {
  loading.value = true
  try {
    const params: SectionStatisticQueryParams = {}

    // 构建查询参数
    if (formData.value.timeRange[0]) {
      params.startTime = formatTime(formData.value.timeRange[0])
    }
    if (formData.value.timeRange[1]) {
      params.endTime = formatTime(formData.value.timeRange[1])
    }
    if (formData.value.name) {
      params.name = formData.value.name
    }
    if (formData.value.volt) {
      params.volt = formData.value.volt
    }
    if (formData.value.status) {
      params.status = formData.value.status
    }

    const response = await SectionMonitoringService.getSectionStatisticList(params)

    // 响应拦截器已经处理了数据格式，response直接就是数据数组
    if (response && Array.isArray(response)) {
      const mappedData = response.map((item: SectionStatisticData) => ({
        id: item.id,
        name: item.name,
        volt: item.volt,
        limit: item.limit,
        maxValue: item.maxValue,
        maxDiffValue: item.maxDiffValue,
        totalOverTime: item.totalOverTime,
        longestOverTime: item.longestOverTime,
        maxValueTime: item.maxValueTime,
      }))

      // 应用默认排序
      tableData.value = applyDefaultSort(mappedData)
    }
  } catch (error) {
    console.error('获取断面统计数据失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  fetchSectionStatisticList()
}

// 重置处理函数
const handleReset = () => {
  // 重置表单数据
  formData.value = {
    timeRange: [timestampRange[0], timestampRange[1]],
    name: '',
    volt: null,
    status: null,
  }
  // 重新获取数据
  fetchSectionStatisticList()
}

// 排序处理函数
const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  console.log('概览页排序:', column.key, order, multiSortState)

  if (!order && Object.keys(multiSortState).length === 0) {
    // 恢复默认排序
    // fetchSectionStatisticList()
    tableData.value = applyDefaultSort(tableData.value)
    return
  }

  // 使用公共的多列排序函数
  tableData.value = handleMultiSort(tableData.value, multiSortState, columns)
}

// 查看详情处理函数
const handleViewDetail = (item: TableRowData, index: number) => {
  console.log('查看详情:', item, index)
  sectionMonitoringStore.selectedTableRow = item
  // 同步时间范围到详情页面
  sectionMonitoringStore.overviewTimeRange = formData.value.timeRange
  // 这里可以添加查看详情的逻辑，比如打开详情弹窗或跳转到详情页面
}

// 去除详情列
const columnsByExport = computed(() => {
  return columns.filter((item: any) => item.key !== 'action')
})

// 电压等级变化处理函数 当电压等级为220时默认越限
const handleVoltChange = (value: string | null) => {
  if (value === '220KV') {
    formData.value.status = '越限'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSectionStatisticList()
})
</script>
<style></style>
