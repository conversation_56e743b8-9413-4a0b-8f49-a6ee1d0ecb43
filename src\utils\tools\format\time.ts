import dayjs from 'dayjs'

/**
 *  格式化时间
 * @param time
 * @param format
 * @returns
 */
export const formatTime = (time: number | string, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(time).format(format)
}

// 获取当日0点到23:59:59的时间戳
export const getTodayTimeRange = () => {
  const start = dayjs().startOf('day').valueOf()
  const end = dayjs().endOf('day').valueOf()
  return [start, end]
}

// 判断时间范围是否跨天
export const isTimeRangeCrossDay = (timeRange: [number, number] | null): boolean => {
  if (!timeRange || timeRange.length !== 2) {
    return false
  }

  const [startTime, endTime] = timeRange
  const startDay = dayjs(startTime).format('YYYY-MM-DD')
  const endDay = dayjs(endTime).format('YYYY-MM-DD')

  return startDay !== endDay
}

/**
 *  禁用选择器中之前的时间
 * @param ts
 * @returns
 */
export const disablePreviousDate = (ts: number) => {
  return ts > getTodayTimeRange()[1]
}
