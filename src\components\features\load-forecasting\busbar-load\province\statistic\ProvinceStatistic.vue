<template>
  <div class="flex flex-col h-full">
    <!-- 顶部搜索表单 -->
    <SearchForm :loading="loading" @search="handleSearch">
      <template #form-fields>
        <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
          <n-date-picker
            v-model:value="busbarLoadStore.timeRange"
            class="w-full"
            type="datetimerange"
            size="large"
            placeholder="请选择时间范围"
            :is-date-disabled="disablePreviousDate"
          >
          </n-date-picker>
        </InputGroup>
      </template>

      <template #action-buttons>
        <ExportButton :data="tableData" :columns="tableColumns" filename="全省母线负荷数据" />
      </template>
    </SearchForm>
    <LineChart title="偏差双Y轴曲线" :data="chartData" height="450px" class="mt-2" />

    <!-- 底部数据展示区域 -->
    <DataTable
      class="px-5"
      :columns="tableColumns"
      :data="tableData"
      :loading="loading"
      height="calc(100vh - 595px)"
      @sort="handleSort"
    >
      <template #action="{ item, index }">
        <n-button type="primary" @click="handleViewDetail(item, index)"> 查看 </n-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, toRaw } from 'vue'
import { NDatePicker, NButton } from 'naive-ui'

import { disablePreviousDate, formatTime } from '@/utils/tools/'
import { useBusbarLoad } from '@/stores'
import { compareNumericDesc, handleMultiSort } from '@/utils/sort'

import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/DataTable.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import ExportButton from '@/components/shared/ExportButton.vue'
import {
  BusbarLoadService,
  type BusbarLoadDataItem,
  type BusbarLoadQueryParams,
} from '@/utils/api/services/busbarLoad'

// 默认表格列配置
const tableColumns = [
  {
    key: 'time',
    title: '时间',
    sorter: {
      multiple: 2,
    },
  },
  {
    key: 'rtValue',
    title: '负荷实测',
    sorter: {
      multiple: 2,
    },
  },
  {
    key: 'usPrediction',
    title: '负荷预测值',
    sorter: {
      multiple: 2,
    },
  },
  {
    key: 'usDiff',
    title: '负荷偏差',
    sorter: {
      multiple: 2,
    },
  },
  {
    key: 'action',
    title: '详情',
  },
]

const busbarLoadStore = useBusbarLoad()
const loading = ref(false)
const rowData = ref<BusbarLoadDataItem[]>([])
const tableData = ref<BusbarLoadDataItem[]>([])

const chartData = computed(() => {
  if (!rowData.value.length) return []

  return [
    {
      name: '负荷预测',
      color: '#CE42AE',
      data: rowData.value.map((item) => ({
        name: item.time,
        value: parseFloat(item.usPrediction) || 0,
      })),
    },
    {
      name: '负荷实测',
      color: '#0CA39F',
      data: rowData.value.map((item) => ({
        name: item.time,
        value: parseFloat(item.rtValue) || 0,
      })),
    },
    {
      name: '负荷偏差',
      color: '#004EE4',
      yAxisIndex: 2,
      data: rowData.value.map((item) => ({
        name: item.time,
        value: parseFloat(item.usDiff) || 0,
      })),
    },
  ]
})

const fetchBusbarLoadData = async () => {
  loading.value = true
  try {
    const params: BusbarLoadQueryParams = {}

    // 构建查询参数
    if (busbarLoadStore.timeRange[0]) {
      params.startTime = formatTime(busbarLoadStore.timeRange[0])
    }
    if (busbarLoadStore.timeRange[1]) {
      params.endTime = formatTime(busbarLoadStore.timeRange[1])
    }

    rowData.value = await BusbarLoadService.getBusbarLoadData(params)
    tableData.value = JSON.parse(JSON.stringify(toRaw(rowData.value)))
    // 处理响应数据
  } catch (error) {
    console.error('获取全省母线负荷数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  fetchBusbarLoadData()
}

const handleViewDetail = (item: BusbarLoadDataItem, index: number) => {
  busbarLoadStore.isInfoVisible = true
}

const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  console.log('概览页排序:', column.key, order, multiSortState)

  if (!order && Object.keys(multiSortState).length === 0) {
    // 恢复默认排序
    tableData.value = JSON.parse(JSON.stringify(toRaw(rowData.value)))
    return
  }

  // 使用公共的多列排序函数
  tableData.value = handleMultiSort(tableData.value, multiSortState, tableColumns)
}

onMounted(() => {
  handleSearch()
})
</script>

<style></style>
