<template>
  <div class="flex flex-col">
    <!-- 表格容器 -->
    <div class="flex flex-1 bg-white">
      <!-- 表头 -->
      <div
        class="flex flex-col w-33 px-3 mr-2.5 rounded overflow-hidden text-16px leading-22px border border-solid border-#A9C0EA text-#6D84AE bg-#E4EBF8"
      >
        <div
          v-for="(column, index) in columns"
          :key="column.key"
          class="py-2 mb-1.75 flex"
          :class="[{ 'flex-1': !column.height }, { 'items-center': index !== columns.length - 1 }]"
          :style="{ justifyContent: column.align || 'left', height: column.height }"
        >
          {{ column.title }}
        </div>
      </div>
      <!-- 表格内容 -->
      <!-- <n-scrollbar x-scrollable class="w-[calc(100vw-700px)] h-full flex"> -->
      <div class="flex overflow-x-auto v-scroll" :style="{ width }">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="flex flex-shrink-0 flex-col w-39.5 mr-2.5 rounded"
        >
          <div
            v-for="(column, colIndex) in columns"
            :key="column.key"
            class="px-4 py-2 flex mb-1.75 rounded text-20px bg-[#F8F8F8]"
            :class="[
              { 'flex-1': !column.height },
              { 'items-center': colIndex !== columns.length - 1 },
            ]"
            :style="{
              color: '#5F6673',
              justifyContent: column.align || 'left',
              height: column.height,
            }"
          >
            <slot
              :name="column.key"
              :item="item"
              :value="getColumnValue(item, column.key)"
              :index="index"
            >
              {{ getColumnValue(item, column.key) }}
            </slot>
          </div>
        </div>
      </div>
      <!-- </n-scrollbar> -->
    </div>
    <!-- Loading 遮罩 -->
    <div
      v-if="loading"
      class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"
    >
      <n-spin size="large" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { NSpin, NScrollbar } from 'naive-ui'

// 定义接口
interface TableColumn {
  key: string
  title: string
  width?: string
  height?: string
  align?: 'left' | 'center' | 'right'
}

// 定义 props
interface Props {
  columns: TableColumn[]
  data: Record<string, any>[]
  height?: string
  width?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  width: '100%',
  loading: false,
})

// 方法
const getColumnValue = (item: Record<string, any>, key: string) => {
  return item[key] || ''
}
</script>

<style scoped>
.v-scroll {
  overflow: auto;
  scrollbar-width: thin; /* Firefox */
}
</style>
