<template>
  <div class="flex flex-col h-100vh">
    <div class="flex px-9 py-4 bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
      <div class="flex flex-1 text-2xl text-#9E9E9E">{{ timeRangeStr }}</div>
      <div class="flex">
        <ExportCps class="mr-2.5" />

        <n-button type="info" size="large" @click="handleBack">
          <template #icon>
            <n-icon><ChevronLeft20FilledIcon /></n-icon>
          </template>
          返回
        </n-button>
      </div>
    </div>

    <CpsChart />

    <!-- 底部数据展示区域 -->
    <CpsBottomInfo></CpsBottomInfo>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import CpsBottomInfo from './CpsBottomInfo.vue'
import { ChevronLeft20FilledIcon } from '@/utils/constant/icons'
import ExportCps from '../ExportCps.vue'
import CpsChart from '../CpsChart.vue'
import { useCpsFrequency } from '@/stores'
import { formatTime } from '@/utils/tools'
import { useCpsData } from '../useCpsData'

const cpsFrequencyStore = useCpsFrequency()

useCpsData()

const timeRangeStr = computed(() => {
  if (!cpsFrequencyStore.timeRange) return ''
  return `${formatTime(cpsFrequencyStore.timeRange[0])} ~ ${formatTime(cpsFrequencyStore.timeRange[1])}`
})

const handleBack = () => {
  cpsFrequencyStore.isInfoVisible = false
}

onBeforeUnmount(() => {
  cpsFrequencyStore.isInfoVisible = false
})
</script>

<style scoped></style>
