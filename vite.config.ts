import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import VueDevTools from 'vite-plugin-vue-devtools'
import UnoCSS from 'unocss/vite'
import svgLoader from 'vite-svg-loader'
import legacy from '@vitejs/plugin-legacy'

// https://vite.dev/config/
export default defineConfig({
  base: './',
  plugins: [VueDevTools(), vue(), UnoCSS(), svgLoader(), legacy()],

  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    proxy: {
      '/dwyzt': {
        target: 'http://*************:8087/mock/189',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dwyzt/, '/dwyzt'),
      },
    },
  },
})
