<template>
  <n-button size="large" @click="handleExport">
    <template #icon>
      <n-icon><ExportIcon /></n-icon>
    </template>
    导出表格
  </n-button>
</template>

<script setup lang="ts">
import { NButton, NIcon } from 'naive-ui'
import { ExportIcon } from '@/utils/constant/icons'
import { exportCpsExcel } from '@/utils/export'
import { useCpsFrequency } from '@/stores'

const cpsFrequencyStore = useCpsFrequency()

// 导出处理函数
const handleExport = () => {
  exportCpsExcel(
    cpsFrequencyStore.cpsData?.CPSStatisticList || [],
    cpsFrequencyStore.cpsData?.JSRateList || [],
    'CPS及频率数据',
    cpsFrequencyStore.chartRef?.getImg().base64 || '',
  )
}
</script>

<style scoped></style>
