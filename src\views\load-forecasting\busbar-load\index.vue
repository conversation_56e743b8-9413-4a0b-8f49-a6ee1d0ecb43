<template>
  <template v-if="isProvince">
    <Province />
  </template>
  <template v-else>
    <City />
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Province from '@/components/features/load-forecasting/busbar-load/province/index.vue'
import City from '@/components/features/load-forecasting/busbar-load/city/index.vue'

const route = useRoute()

const isProvince = computed(() => route.query.key === 'province' || !route.query.key)
</script>

<style scoped></style>
